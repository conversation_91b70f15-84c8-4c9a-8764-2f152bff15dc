from pymongo import MongoClient
import bcrypt
from bson.objectid import ObjectId
import time

# MongoDB connection
MONGO_URI = "mongodb://8.134.100.40:27017/?tlsInsecure=true&tls=false"
client = None
db = None
use_fallback = False
fallback_users = {}  # In-memory fallback storage

def init_db():
    """Initialize MongoDB connection with fallback"""
    global client, db, use_fallback
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)
        db = client.user_management
        # Test connection
        client.admin.command('ping')
        print("MongoDB connection successful")
        use_fallback = False
    except Exception as e:
        print(f"MongoDB connection failed: {e}")
        print("Using in-memory fallback storage")
        use_fallback = True

def get_db():
    """Get database instance"""
    return db

class User:
    def __init__(self, email, password, role='user'):
        self.email = email
        self.password = self._hash_password(password)
        self.role = role
    
    @staticmethod
    def _hash_password(password):
        """Hash password using bcrypt"""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    @staticmethod
    def _check_password(password, hashed):
        """Check password against hash"""
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    
    def save(self):
        """Save user to database or fallback storage"""
        if use_fallback:
            user_id = str(int(time.time() * 1000000))  # Simple ID generation
            fallback_users[user_id] = {
                '_id': user_id,
                'email': self.email,
                'password': self.password,
                'role': self.role
            }
            return user_id
        else:
            users_collection = db.users
            user_data = {
                'email': self.email,
                'password': self.password,
                'role': self.role
            }
            result = users_collection.insert_one(user_data)
            return result.inserted_id
    
    @staticmethod
    def find_by_email(email):
        """Find user by email"""
        if use_fallback:
            for user in fallback_users.values():
                if user['email'] == email:
                    return user
            return None
        else:
            users_collection = db.users
            return users_collection.find_one({'email': email})
    
    @staticmethod
    def find_by_id(user_id):
        """Find user by ID"""
        if use_fallback:
            return fallback_users.get(user_id)
        else:
            users_collection = db.users
            return users_collection.find_one({'_id': ObjectId(user_id)})
    
    @staticmethod
    def get_all_users():
        """Get all users"""
        if use_fallback:
            return [{'email': user['email'], 'role': user['role']} for user in fallback_users.values()]
        else:
            users_collection = db.users
            return list(users_collection.find({}, {'password': 0}))  # Exclude password field
    
    @staticmethod
    def authenticate(email, password):
        """Authenticate user"""
        user = User.find_by_email(email)
        if user and User._check_password(password, user['password']):
            return user
        return None
    
    def to_dict(self):
        """Convert user to dictionary"""
        return {
            'email': self.email,
            'role': self.role
        }

def create_admin_user():
    """Create default admin user if not exists"""
    admin_email = "<EMAIL>"
    admin_password = "admin2025#"
    
    # Check if admin user already exists
    existing_admin = User.find_by_email(admin_email)
    if not existing_admin:
        admin_user = User(admin_email, admin_password, 'admin')
        admin_user.save()
        print(f"Admin user created: {admin_email}")
    else:
        print("Admin user already exists")

