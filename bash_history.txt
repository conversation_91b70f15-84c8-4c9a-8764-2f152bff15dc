export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>/home/<USER>
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && pip install pymongo flask-cors flask-jwt-extended bcrypt
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && pip freeze > requirements.txt
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>//localhost:5000/api/users -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmcmVzaCI6ZmFsc2UsImlhdCI6MTc1MTk1NDE3MSwianRpIjoiZTEwM2UxODEtZmYxZi00NTBkLWE4MzYtMTNhMDRlODU4ZmJhIiwidHlwZSI6ImFjY2VzcyIsInN1YiI6IjE3NTE5NTQxNDE3MjI3MzgiLCJuYmYiOjE3NTE5NTQxNzEsImNzcmYiOiIzMDI2NDc5Zi05YTI5LTQzN2EtYThkMC0xMGM2OGU1Y2Q2NzAiLCJleHAiOjE3NTE5NTUwNzF9.ar_aAFvB_IbU_Q5kh_trdxRljFuOYxP0_3V1bkqnOT8"
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && python src/main.py
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && timeout 10 python src/main.py
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && python -c "from src.models.user import init_db; init_db()"
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>//localhost:5000/api/register -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"test123"}'
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && timeout 5 python src/main.py
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>//localhost:5000/api/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"admin2025#"}'
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>/user_management_backend && cd /home/<USER>/user_management_backend && source venv/bin/activate && python src/main.py
export PS1="[CMD_BEGIN]\n\u@\h:\w\n[CMD_END]"; export PS2=""
export TERM=xterm-256color
cd /home/<USER>//localhost:5000/api/register -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"test123"}'
