<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理系统</title>
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.21.1/dist/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 40px;
            width: 100%;
            max-width: 500px;
            margin: 20px;
        }
        
        .admin-container {
            max-width: 800px;
        }
        
        h1, h2 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 500;
        }
        
        input[type="email"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="email"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .error {
            color: #dc3545;
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: #f8d7da;
            border-radius: 5px;
        }
        
        .success {
            color: #155724;
            text-align: center;
            margin-bottom: 20px;
            padding: 10px;
            background: #d4edda;
            border-radius: 5px;
        }
        
        .toggle-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .toggle-link a {
            color: #667eea;
            text-decoration: none;
            cursor: pointer;
        }
        
        .toggle-link a:hover {
            text-decoration: underline;
        }
        
        .user-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .users-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .user-item {
            padding: 10px;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-item:last-child {
            border-bottom: none;
        }
        
        .user-email {
            font-weight: 500;
            color: #333;
        }
        
        .loading {
            text-align: center;
            color: #666;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录/注册表单 -->
        <div v-if="!isLoggedIn" class="container">
            <h1>{{ isLogin ? '用户登录' : '用户注册' }}</h1>
            
            <div v-if="error" class="error">{{ error }}</div>
            <div v-if="success" class="success">{{ success }}</div>
            
            <form @submit.prevent="isLogin ? login() : register()">
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input 
                        type="email" 
                        id="email" 
                        v-model="email" 
                        required 
                        placeholder="请输入邮箱地址"
                    >
                </div>
                
                <div class="form-group">
                    <label for="password">密码</label>
                    <input 
                        type="password" 
                        id="password" 
                        v-model="password" 
                        required 
                        placeholder="请输入密码"
                    >
                </div>
                
                <button type="submit" class="btn" :disabled="loading">
                    {{ loading ? '处理中...' : (isLogin ? '登录' : '注册') }}
                </button>
            </form>
            
            <div class="toggle-link">
                <a @click="toggleMode">
                    {{ isLogin ? '没有账户？点击注册' : '已有账户？点击登录' }}
                </a>
            </div>
        </div>
        
        <!-- 用户仪表板 -->
        <div v-if="isLoggedIn" class="container" :class="{ 'admin-container': user.role === 'admin' }">
            <h1>欢迎，{{ user.email }}</h1>
            
            <div class="user-info">
                <p><strong>邮箱：</strong>{{ user.email }}</p>
                <p><strong>角色：</strong>{{ user.role === 'admin' ? '管理员' : '普通用户' }}</p>
            </div>
            
            <!-- 管理员用户列表 -->
            <div v-if="user.role === 'admin'">
                <h2>用户列表</h2>
                <div v-if="loadingUsers" class="loading">加载用户列表中...</div>
                <div v-else-if="users.length > 0" class="users-list">
                    <div v-for="(userItem, index) in users" :key="index" class="user-item">
                        <span class="user-email">{{ userItem.email }}</span>
                    </div>
                </div>
                <div v-else class="loading">暂无用户数据</div>
            </div>
            
            <!-- 普通用户欢迎信息 -->
            <div v-if="user.role === 'user'">
                <div class="user-info">
                    <p>欢迎使用用户管理系统！您已成功登录。</p>
                </div>
            </div>
            
            <button @click="logout" class="btn btn-danger">退出登录</button>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                isLogin: true,
                isLoggedIn: false,
                email: '',
                password: '',
                user: null,
                users: [],
                error: '',
                success: '',
                loading: false,
                loadingUsers: false,
                token: ''
            },
            mounted() {
                // 检查本地存储的token
                const savedToken = localStorage.getItem('token');
                const savedUser = localStorage.getItem('user');
                if (savedToken && savedUser) {
                    this.token = savedToken;
                    this.user = JSON.parse(savedUser);
                    this.isLoggedIn = true;
                    if (this.user.role === 'admin') {
                        this.loadUsers();
                    }
                }
            },
            methods: {
                toggleMode() {
                    this.isLogin = !this.isLogin;
                    this.error = '';
                    this.success = '';
                    this.email = '';
                    this.password = '';
                },
                
                async register() {
                    this.loading = true;
                    this.error = '';
                    this.success = '';
                    
                    try {
                        const response = await axios.post('/api/register', {
                            email: this.email,
                            password: this.password
                        });
                        
                        this.success = '注册成功！请登录。';
                        this.email = '';
                        this.password = '';
                        setTimeout(() => {
                            this.isLogin = true;
                            this.success = '';
                        }, 2000);
                        
                    } catch (error) {
                        this.error = error.response?.data?.error || '注册失败，请重试。';
                    } finally {
                        this.loading = false;
                    }
                },
                
                async login() {
                    this.loading = true;
                    this.error = '';
                    
                    try {
                        const response = await axios.post('/api/login', {
                            email: this.email,
                            password: this.password
                        });
                        
                        this.token = response.data.access_token;
                        this.user = response.data.user;
                        this.isLoggedIn = true;
                        
                        // 保存到本地存储
                        localStorage.setItem('token', this.token);
                        localStorage.setItem('user', JSON.stringify(this.user));
                        
                        // 如果是管理员，加载用户列表
                        if (this.user.role === 'admin') {
                            this.loadUsers();
                        }
                        
                    } catch (error) {
                        this.error = error.response?.data?.error || '登录失败，请检查邮箱和密码。';
                    } finally {
                        this.loading = false;
                    }
                },
                
                async loadUsers() {
                    this.loadingUsers = true;
                    
                    try {
                        const response = await axios.get('/api/users', {
                            headers: {
                                'Authorization': `Bearer ${this.token}`
                            }
                        });
                        
                        this.users = response.data.users;
                        
                    } catch (error) {
                        this.error = '加载用户列表失败。';
                    } finally {
                        this.loadingUsers = false;
                    }
                },
                
                logout() {
                    this.isLoggedIn = false;
                    this.user = null;
                    this.users = [];
                    this.token = '';
                    this.email = '';
                    this.password = '';
                    this.error = '';
                    this.success = '';
                    
                    // 清除本地存储
                    localStorage.removeItem('token');
                    localStorage.removeItem('user');
                }
            }
        });
    </script>
</body>
</html>

