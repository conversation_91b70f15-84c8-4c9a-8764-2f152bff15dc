# 系统设计文档

## 1. 系统架构

本系统将采用前后端分离的架构：

- **前端**: 使用Vue2构建，负责用户界面展示和与后端API的交互。
- **后端**: 使用Python Flask构建，提供RESTful API，处理业务逻辑和数据存储。
- **数据库**: 使用MongoDB，存储用户数据。

## 2. 数据库设计

数据库将包含一个 `users` 集合，用于存储用户信息。每个用户文档（document）将包含以下字段：

- `_id`: MongoDB 自动生成的唯一标识符。
- `email`: 用户的电子邮件地址，作为唯一标识（字符串类型）。
- `password`: 用户的密码（字符串类型，**存储时应进行哈希处理**）。
- `role`: 用户的角色，例如 'user' 或 'admin'（字符串类型）。

### 示例用户文档结构：

```json
{
  "_id": ObjectId("..."),
  "email": "<EMAIL>",
  "password": "hashed_password_string",
  "role": "user"
}
```

### 初始管理员账户：

系统启动时，如果 `<EMAIL>` 账户不存在，将自动创建以下管理员账户：

- **Email**: `<EMAIL>`
- **Password**: `admin2025#` (初始密码，用户登录后应提示修改)
- **Role**: `admin`

## 3. 后端API设计 (Flask)

后端将提供以下API接口：

- **用户注册**: `POST /api/register`
  - 请求体: `{"email": "", "password": ""}`
  - 响应: 成功则返回用户信息，失败则返回错误信息。

- **用户登录**: `POST /api/login`
  - 请求体: `{"email": "", "password": ""}`
  - 响应: 成功则返回认证token和用户信息，失败则返回错误信息。

- **获取用户列表 (管理员专用)**: `GET /api/users`
  - 请求头: 包含认证token
  - 响应: 成功则返回所有用户（或仅包含email）的列表，失败则返回错误信息（例如权限不足）。

## 4. 前端界面设计 (Vue2)

前端将包含以下页面：

- **注册页面**: 允许用户输入邮箱和密码进行注册。
- **登录页面**: 允许用户输入邮箱和密码进行登录。
- **用户列表页面 (管理员可见)**: 登录后，如果用户角色为管理员，则显示所有注册用户的邮箱列表。
- **普通用户页面**: 登录后，如果用户角色为普通用户，则显示欢迎信息。

## 5. 安全考虑

- **密码哈希**: 用户密码在存储到MongoDB之前必须进行哈希处理。
- **认证**: 使用JWT (JSON Web Tokens) 进行用户认证。
- **权限控制**: 后端API将根据用户角色进行权限验证，例如只有管理员才能访问用户列表。
- **TLS/SSL**: 尽管用户提供了 `tlsInsecure=true&tls=false`，但在生产环境中应使用安全的TLS/SSL连接到MongoDB。


